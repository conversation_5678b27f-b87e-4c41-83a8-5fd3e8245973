from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session, send_file, Response
import os
import sys
import secrets
from werkzeug.utils import secure_filename
from hr_database_working import HRDatabase
from datetime import datetime, date
import glob
from functools import wraps
import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment
from io import BytesIO

# Import enhanced modules
from language_detector import LanguageDetector
from hybrid_german_cv_parser import HybridGermanCVParser
from bilingual_matcher import BilingualCVMatcher
from translation_service import TranslationService, get_text
from config import BilingualConfig, get_supported_languages
from job_specific_matcher import JobSpecificMatcher

# Simple document processing functions
def extract_text_from_pdf(filepath):
    """Extract text from PDF using PyMuPDF"""
    try:
        import fitz
        doc = fitz.open(filepath)
        text = ""
        for page in doc:
            text += page.get_text()
        doc.close()
        return text
    except Exception as e:
        print(f"Error extracting PDF text: {e}")
        return ""

def extract_text_from_docx(filepath):
    """Extract text from DOCX using python-docx"""
    try:
        from docx import Document
        doc = Document(filepath)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text
    except Exception as e:
        print(f"Error extracting DOCX text: {e}")
        return ""

app = Flask(__name__)
app.secret_key = 'hr_management_secret_key_change_in_production'

# Use absolute path for upload folder to ensure consistency
script_dir = os.path.dirname(os.path.abspath(__file__))
app.config['UPLOAD_FOLDER'] = os.path.join(script_dir, 'uploads')
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max upload size
app.config['ALLOWED_EXTENSIONS'] = {'pdf', 'docx'}

# Add custom filter for newlines to <br> conversion
@app.template_filter('nl2br')
def nl2br_filter(text):
    """Convert newlines to <br> tags"""
    if text:
        return text.replace('\n', '<br>')
    return text

# Create uploads folder if it doesn't exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Initialize database and enhanced components
hr_db = HRDatabase()
cv_matcher = JobSpecificMatcher()  # Enhanced job-specific matcher
bilingual_matcher = BilingualCVMatcher()
bilingual_extractor = HybridGermanCVParser()  # Using enhanced hybrid parser with perfect parser
language_detector = LanguageDetector()
translation_service = TranslationService()

# Authentication decorator
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# Context processor for templates
@app.context_processor
def inject_now():
    current_language = session.get('language', BilingualConfig.DEFAULT_LANGUAGE)
    return {
        'now': datetime.now(),
        'current_language': current_language,
        'supported_languages': get_supported_languages(),
        'get_text': lambda key: get_text(key, current_language)
    }

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

def process_document(filepath):
    """Process document and extract text"""
    filename = os.path.basename(filepath)
    file_ext = os.path.splitext(filename)[1].lower()

    if file_ext == '.pdf':
        content = extract_text_from_pdf(filepath)
    elif file_ext == '.docx':
        content = extract_text_from_docx(filepath)
    else:
        return None

    return content

# Authentication routes
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '').strip()

        if not username or not password:
            flash('Please provide both username and password', 'error')
            return render_template('login.html')

        user = hr_db.authenticate_user(username, password)
        if user:
            if user.is_locked():
                current_language = session.get('language', 'en')
                flash(get_text('account_locked', current_language), 'error')
                return render_template('login.html')

            session['user_id'] = user.id
            session['username'] = user.username
            session['csrf_token'] = secrets.token_hex(16)  # Add CSRF protection
            flash(f'Welcome back, {user.username}!', 'success')
            return redirect(url_for('home'))
        else:
            current_language = session.get('language', 'en')
            flash('Invalid username or password', 'error')

    return render_template('login.html')

@app.route('/logout')
def logout():
    session.clear()
    flash('You have been logged out successfully', 'info')
    return redirect(url_for('login'))

@app.route('/switch_language/<language>')
def switch_language(language):
    """Switch application language"""
    if language in get_supported_languages():
        session['language'] = language
        flash(f'Language switched to {language.title()}', 'info')
    else:
        flash('Unsupported language', 'error')

    # Redirect back to the referring page or home
    return redirect(request.referrer or url_for('home'))

@app.route('/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Change user password"""
    current_language = session.get('language', 'en')

    if request.method == 'POST':
        current_password = request.form.get('current_password', '').strip()
        new_password = request.form.get('new_password', '').strip()
        confirm_password = request.form.get('confirm_password', '').strip()

        # Validation
        if not current_password or not new_password or not confirm_password:
            flash('All fields are required', 'error')
            return render_template('change_password.html')

        if new_password != confirm_password:
            flash(get_text('passwords_dont_match', current_language), 'error')
            return render_template('change_password.html')

        if len(new_password) < 8:
            flash(get_text('password_requirements', current_language), 'error')
            return render_template('change_password.html')

        try:
            user_id = session.get('user_id')
            hr_db.change_user_password(user_id, current_password, new_password)
            flash(get_text('password_changed_successfully', current_language), 'success')
            return redirect(url_for('home'))
        except ValueError as e:
            if 'Current password is incorrect' in str(e):
                flash(get_text('current_password_incorrect', current_language), 'error')
            else:
                flash(str(e), 'error')
            return render_template('change_password.html')
        except Exception as e:
            flash(f'Error changing password: {str(e)}', 'error')
            return render_template('change_password.html')

    return render_template('change_password.html')

@app.route('/')
@login_required
def home():
    """Home page / Dashboard"""
    jobs_count = len(hr_db.get_all_jobs())

    # Get list of logo images from static/img folder
    logo_images = []
    static_img_dir = os.path.join(os.path.dirname(__file__), 'static', 'img')
    if os.path.exists(static_img_dir):
        for ext in ['png', 'jpg', 'jpeg', 'gif']:
            logo_images.extend([os.path.basename(f) for f in glob.glob(os.path.join(static_img_dir, f'*.{ext}'))])

    # Limit to 3 images if more are found
    logo_images = logo_images[:3]

    return render_template('index.html', jobs_count=jobs_count, logo_images=logo_images)

@app.route('/jobs')
@login_required
def jobs():
    """List all jobs"""
    all_jobs = hr_db.get_all_jobs()

    # Create a list of dictionaries with job info and CV count
    jobs_with_counts = []
    for job in all_jobs:
        cv_count = len(hr_db.get_cvs_for_job(job.title))

        # Get main responsible person safely
        main_responsible = "Not Assigned"
        responsible_people = hr_db.get_responsible_people_for_job(job.id)
        for person in responsible_people:
            if person.is_main_responsible:
                main_responsible = person.name
                break

        # Calculate days remaining safely
        days_remaining = None
        if job.end_date:
            today = date.today()
            days_remaining = (job.end_date - today).days

        jobs_with_counts.append({
            'id': job.id,
            'title': job.title,
            'description': job.description,
            'platform': job.platform,
            'job_url': job.job_url,
            'start_date': job.start_date,
            'end_date': job.end_date,
            'status': job.status,
            'days_remaining': days_remaining,
            'main_responsible_person': main_responsible,
            'cv_count': cv_count
        })

    return render_template('jobs.html', jobs=jobs_with_counts)

@app.route('/jobs/add', methods=['GET', 'POST'])
@login_required
def add_job():
    """Add a new job"""
    if request.method == 'POST':
        title = request.form.get('title', '').strip()
        description = request.form.get('description', '').strip()
        platform = request.form.get('platform', '').strip()
        job_url = request.form.get('job_url', '').strip()
        status = request.form.get('status', 'Active').strip()
        start_date_str = request.form.get('start_date', '').strip()
        end_date_str = request.form.get('end_date', '').strip()
        main_name = request.form.get('main_name', '').strip()
        main_email = request.form.get('main_email', '').strip()

        if not title or not description:
            flash('Please provide both a title and description', 'error')
            return render_template('add_job.html')

        if not main_name or not main_email:
            flash('Please provide main responsible person details', 'error')
            return render_template('add_job.html', title=title, description=description)

        # Parse dates
        start_date = None
        end_date = None
        try:
            if start_date_str:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            if end_date_str:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            flash('Invalid date format', 'error')
            return render_template('add_job.html', title=title, description=description)

        try:
            # Prepare responsible people data
            responsible_people = [
                {
                    'name': main_name,
                    'email': main_email,
                    'is_main': True
                }
            ]

            # Add additional responsible people
            additional_names = request.form.getlist('additional_names[]')
            additional_emails = request.form.getlist('additional_emails[]')

            for name, email in zip(additional_names, additional_emails):
                if name.strip() and email.strip():
                    responsible_people.append({
                        'name': name.strip(),
                        'email': email.strip(),
                        'is_main': False
                    })

            hr_db.add_job(
                title=title,
                description=description,
                responsible_people=responsible_people,
                platform=platform if platform else None,
                job_url=job_url if job_url else None,
                start_date=start_date,
                end_date=end_date,
                status=status
            )
            flash(f'Job "{title}" added successfully with {len(responsible_people)} responsible people!', 'success')
            return redirect(url_for('jobs'))
        except ValueError as e:
            flash(str(e), 'error')
            return render_template('add_job.html', title=title, description=description)

    return render_template('add_job.html')

@app.route('/jobs/<job_title>')
@login_required
def job_detail(job_title):
    """Display detailed job information"""
    try:
        # Get job details
        job = hr_db.get_job_by_title(job_title)
        if not job:
            flash(f'Job "{job_title}" not found', 'error')
            return redirect(url_for('jobs'))

        # Get responsible people
        responsible_people = hr_db.get_responsible_people_for_job(job.id)

        # Get CV count
        cv_count = len(hr_db.get_cvs_for_job(job_title))

        # Calculate days remaining
        days_remaining = None
        if job.end_date:
            today = date.today()
            days_remaining = (job.end_date - today).days

        # Create job data dictionary
        job_data = {
            'id': job.id,
            'title': job.title,
            'description': job.description,
            'platform': job.platform,
            'job_url': job.job_url,
            'start_date': job.start_date,
            'end_date': job.end_date,
            'status': job.status,
            'days_remaining': days_remaining,
            'cv_count': cv_count,
            'responsible_people': responsible_people
        }

        return render_template('job_detail.html', job=job_data)

    except Exception as e:
        flash(f'Error loading job details: {str(e)}', 'error')
        return redirect(url_for('jobs'))

@app.route('/jobs/delete/<job_title>')
@login_required
def delete_job(job_title):
    """Delete a job and all associated CVs"""
    try:
        # Get CV count for confirmation message
        cvs = hr_db.get_cvs_for_job(job_title)
        cv_count = len(cvs)

        # Delete job (this will cascade delete CVs and responsible people)
        hr_db.delete_job(job_title)

        if cv_count > 0:
            flash(f'Job "{job_title}" and {cv_count} associated CVs deleted successfully!', 'success')
        else:
            flash(f'Job "{job_title}" deleted successfully!', 'success')
    except Exception as e:
        flash(f'Error deleting job: {str(e)}', 'error')
    return redirect(url_for('jobs'))

@app.route('/cvs')
@login_required
def cvs():
    """List all CVs by job"""
    job_title = request.args.get('job')
    all_jobs = hr_db.get_all_jobs()

    # Convert all_jobs to a list of dictionaries with just the title
    jobs_list = [{'title': job.title} for job in all_jobs]

    if job_title:
        job_cvs = hr_db.get_cvs_for_job(job_title)

        # Convert job_cvs to a list of dictionaries
        cvs_list = []
        for cv in job_cvs:
            cvs_list.append({
                'id': cv.id,
                'filename': cv.filename,
                'candidate_name': getattr(cv, 'candidate_name', None) or 'Unknown'
            })

        return render_template('cvs.html', jobs=jobs_list, selected_job=job_title, cvs=cvs_list)

    return render_template('cvs.html', jobs=jobs_list)

@app.route('/cvs/upload', methods=['GET', 'POST'])
@login_required
def upload_cv():
    """Upload a CV for a job"""
    all_jobs = hr_db.get_all_jobs()

    if not all_jobs:
        flash('Please add a job first before uploading CVs', 'error')
        return redirect(url_for('add_job'))

    if request.method == 'POST':
        job_title = request.form.get('job')
        candidate_name = request.form.get('candidate_name', '').strip()

        if not job_title:
            flash('Please select a job', 'error')
            return render_template('upload_cv.html', jobs=all_jobs)

        if not candidate_name:
            flash('Please enter candidate name', 'error')
            return render_template('upload_cv.html', jobs=all_jobs, selected_job=job_title)

        # Check if file was uploaded
        if 'cv_file' not in request.files:
            flash('No file selected', 'error')
            return render_template('upload_cv.html', jobs=all_jobs, selected_job=job_title, candidate_name=candidate_name)

        file = request.files['cv_file']

        if file.filename == '':
            flash('No file selected', 'error')
            return render_template('upload_cv.html', jobs=all_jobs, selected_job=job_title, candidate_name=candidate_name)

        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)

            # Extract content based on file type
            content = process_document(filepath)
            if not content:
                flash('Could not extract content from file', 'error')
                return render_template('upload_cv.html', jobs=all_jobs, selected_job=job_title, candidate_name=candidate_name)

            try:
                # Detect language and extract additional information
                language_info = language_detector.get_language_info(content)

                # Extract bilingual CV data
                cv_data = bilingual_extractor.extract_cv_data_bilingual(
                    filepath, ['name', 'email', 'phone', 'experience', 'skills', 'education']
                )

                # Add candidate name to filename for better identification
                display_filename = f"{candidate_name}_{filename}"

                # Add to HR database with language information
                hr_db.add_cv(display_filename, content, job_title, candidate_name,
                           language=language_info['primary_language'],
                           extracted_data=cv_data)

                current_language = session.get('language', 'en')
                success_msg = get_text('cv_uploaded_successfully', current_language).format(candidate_name)
                flash(success_msg, 'success')
                return redirect(url_for('cvs', job=job_title))
            except Exception as e:
                flash(f'Error adding CV: {str(e)}', 'error')
                return render_template('upload_cv.html', jobs=all_jobs, selected_job=job_title, candidate_name=candidate_name)

    selected_job = request.args.get('job', '')
    return render_template('upload_cv.html', jobs=all_jobs, selected_job=selected_job)

@app.route('/match')
@login_required
def match():
    """Match CVs to jobs page"""
    all_jobs = hr_db.get_all_jobs()
    # Convert all_jobs to a list of dictionaries with just the title
    jobs_list = [{'title': job.title} for job in all_jobs]
    return render_template('match.html', jobs=jobs_list)

@app.route('/match/results', methods=['POST'])
@login_required
def match_results():
    """Run CV matching for a job"""
    job_title = request.form.get('job')

    if not job_title:
        flash('Please select a job', 'error')
        return redirect(url_for('match'))

    job = hr_db.get_job_by_title(job_title)
    if not job:
        flash(f'Job "{job_title}" not found', 'error')
        return redirect(url_for('match'))

    cvs = hr_db.get_cvs_for_job(job_title)
    if not cvs:
        flash(f'No CVs found for job "{job_title}"', 'error')
        return redirect(url_for('match'))

    # Run bilingual matching
    cv_contents = [cv.content for cv in cvs]
    match_results = bilingual_matcher.match_bilingual(job.description, cv_contents)

    # Format results for display with detailed scoring and save to database
    formatted_results = []
    for i, (score, _, detailed_scores) in enumerate(match_results):
        cv = cvs[i]
        # Handle case where candidate_name might not exist in older records
        candidate_name = getattr(cv, 'candidate_name', None) or 'Unknown'

        # Get detailed explanation
        explanation = bilingual_matcher.get_detailed_match_explanation(job.description, cv.content)

        # Save match score to database
        try:
            import json
            match_details_json = json.dumps({
                'content_similarity': detailed_scores['tf_idf'],
                'keyword_match': detailed_scores['keyword'],
                'skill_match': detailed_scores['skill'],
                'language_bonus': detailed_scores['language_bonus'],
                'cv_language': detailed_scores['cv_language'],
                'job_language': detailed_scores['job_language'],
                'explanation': explanation['explanation'] if 'explanation' in explanation else ''
            })
            hr_db.update_cv_match_score(cv.id, f"{score*100:.1f}%", match_details_json)
        except Exception as e:
            print(f"Error saving match score for CV {cv.id}: {e}")

        formatted_results.append({
            'filename': cv.filename,
            'candidate_name': candidate_name,
            'score': float(score) * 100,  # Convert to percentage
            'detailed_scores': {
                'content_similarity': detailed_scores['tf_idf'] * 100,
                'keyword_match': detailed_scores['keyword'] * 100,
                'skill_match': detailed_scores['skill'] * 100,
                'language_bonus': detailed_scores['language_bonus'] * 100
            },
            'cv_language': detailed_scores['cv_language'],
            'job_language': detailed_scores['job_language'],
            'explanation': explanation
        })

    # Convert job to a dictionary with needed fields
    job_dict = {
        'title': job.title,
        'description': job.description
    }

    return render_template('match_results.html', job=job_dict, results=formatted_results)

@app.route('/extract-excel')
@login_required
def extract_excel():
    """Extract CV data to Excel file for download"""
    job_title = request.args.get('job', '')
    count = request.args.get('count', 'all')
    fields = request.args.get('fields', '').split(',')

    if not job_title or not fields:
        flash('Invalid extraction parameters', 'error')
        return redirect(url_for('jobs'))

    try:
        # Get CVs for the job
        cvs = hr_db.get_cvs_for_job(job_title)

        if not cvs:
            flash(f'No CVs found for job "{job_title}"', 'error')
            return redirect(url_for('jobs'))

        # Limit the number of CVs if specified
        if count != 'all':
            try:
                limit = int(count)
                cvs = cvs[:limit]
            except ValueError:
                pass

        # Initialize bilingual CV extractor
        extractor = bilingual_extractor

        # Create Excel workbook
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = f"CV Data - {job_title}"

        # Create headers
        headers = []
        if 'name' in fields:
            headers.append('Name')
        if 'email' in fields:
            headers.append('Email')
        if 'phone' in fields:
            headers.append('Phone')
        if 'experience' in fields:
            headers.append('Experience')
        if 'skills' in fields:
            headers.append('Skills')
        if 'education' in fields:
            headers.append('Education')
        if 'match_score' in fields:
            headers.append('Match Score')
        headers.append('Filename')

        # Write headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")

        # Extract data from each CV
        for row, cv in enumerate(cvs, 2):
            try:
                # Try to find the actual file
                cv_path = None
                possible_paths = []

                # First try the exact filename from database
                exact_path = os.path.join(app.config['UPLOAD_FOLDER'], cv.filename)
                possible_paths.append(exact_path)

                # If filename has candidate name prefix, try without it
                if '_' in cv.filename:
                    parts = cv.filename.split('_', 1)
                    if len(parts) > 1:
                        original_filename = parts[1]
                        original_path = os.path.join(app.config['UPLOAD_FOLDER'], original_filename)
                        possible_paths.append(original_path)

                # Try to find the file
                for path in possible_paths:
                    if os.path.exists(path):
                        cv_path = path
                        break

                # If still not found, try to find any file that ends with the same name
                if not cv_path:
                    upload_dir = app.config['UPLOAD_FOLDER']
                    if os.path.exists(upload_dir):
                        for file in os.listdir(upload_dir):
                            if cv.filename.endswith(file) or file.endswith(cv.filename.split('_')[-1]):
                                cv_path = os.path.join(upload_dir, file)
                                break

                if cv_path and os.path.exists(cv_path):
                    # Extract data using the CV extractor with candidate name validation
                    if hasattr(extractor, 'extract_cv_data_bilingual'):
                        extracted_data = extractor.extract_cv_data_bilingual(cv_path, fields, cv.candidate_name)
                    else:
                        extracted_data = extractor.extract_cv_data(cv_path, fields, cv.candidate_name)

                    col = 1
                    if 'name' in fields:
                        ws.cell(row=row, column=col, value=extracted_data.get('name', 'N/A'))
                        col += 1
                    if 'email' in fields:
                        ws.cell(row=row, column=col, value=extracted_data.get('email', 'N/A'))
                        col += 1
                    if 'phone' in fields:
                        ws.cell(row=row, column=col, value=extracted_data.get('phone', 'N/A'))
                        col += 1
                    if 'experience' in fields:
                        ws.cell(row=row, column=col, value=extracted_data.get('experience', 'N/A'))
                        col += 1
                    if 'skills' in fields:
                        ws.cell(row=row, column=col, value=extracted_data.get('skills', 'N/A'))
                        col += 1
                    if 'education' in fields:
                        ws.cell(row=row, column=col, value=extracted_data.get('education', 'N/A'))
                        col += 1
                    if 'match_score' in fields:
                        # Simple match score calculation
                        job = hr_db.get_job_by_title(job_title)
                        if job:
                            score = cv_matcher.calculate_match_score(cv_path, job.description)
                            ws.cell(row=row, column=col, value=f"{score:.2f}%")
                        else:
                            ws.cell(row=row, column=col, value="N/A")
                        col += 1

                    # Add filename
                    ws.cell(row=row, column=col, value=cv.filename)
                else:
                    # File not found, add placeholder data
                    for col in range(1, len(headers)):
                        ws.cell(row=row, column=col, value='File not found')
                    ws.cell(row=row, column=len(headers), value=cv.filename)

            except Exception as e:
                print(f"Error processing CV {cv.filename}: {e}")
                # Add error data
                for col in range(1, len(headers)):
                    ws.cell(row=row, column=col, value='Error processing')
                ws.cell(row=row, column=len(headers), value=cv.filename)

        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # Save to BytesIO
        output = BytesIO()
        wb.save(output)
        output.seek(0)

        # Create filename
        safe_job_title = "".join(c for c in job_title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        filename = f"CV_Data_{safe_job_title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        flash(f'Error extracting CV data: {str(e)}', 'error')
        return redirect(url_for('jobs'))

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)

