"""
Hybrid German CV Parser
Combines the best features of both parsers:
- Advanced spaCy skills detection and experience calculation
- Robust rule-based name and contact extraction
"""

import os
import re
from typing import Dict, List
from german_cv_parser_advanced import GermanCVParserAdvanced
from bilingual_cv_extractor_patched import BilingualCVExtractorPatched

class HybridGermanCVParser:
    """Hybrid parser combining spaCy NLP with robust rule-based extraction"""
    
    def __init__(self):
        self.advanced_parser = GermanCVParserAdvanced()
        self.rule_parser = BilingualCVExtractorPatched()
        
    def extract_cv_data(self, file_path: str, fields: List[str] = None) -> Dict[str, str]:
        """Extract CV data using hybrid approach"""
        if fields is None:
            fields = ['name', 'email', 'phone', 'experience', 'skills', 'education', 'seniority']
        
        try:
            # Extract text from file
            text = self._extract_text_from_file(file_path)
            if not text:
                return {"error": "Could not read CV text"}
            
            result = {}
            
            # Use rule-based parser for name, email, phone (proven reliable)
            if any(field in fields for field in ['name', 'email', 'phone']):
                rule_result = self.rule_parser.extract_cv_data(file_path, ['name', 'email', 'phone'])
                if 'name' in fields:
                    result['name'] = rule_result.get('name', '')
                if 'email' in fields:
                    result['email'] = rule_result.get('email', '')
                if 'phone' in fields:
                    result['phone'] = rule_result.get('phone', '')
            
            # Use advanced parser for experience, skills, education (better German support)
            if any(field in fields for field in ['experience', 'skills', 'education']):
                advanced_result = self.advanced_parser.extract_cv_data(file_path, ['experience', 'skills', 'education'])
                
                if 'experience' in fields:
                    # Prefer advanced parser's experience if it has structured data
                    adv_exp = advanced_result.get('experience', '')
                    if adv_exp and ('Positionen' in adv_exp or 'Jahre' in adv_exp):
                        result['experience'] = adv_exp
                    else:
                        # Fallback to rule parser
                        rule_exp = self.rule_parser.extract_cv_data(file_path, ['experience'])
                        result['experience'] = rule_exp.get('experience', '')
                
                if 'skills' in fields:
                    # Combine skills from both parsers
                    adv_skills = advanced_result.get('skills', '')
                    rule_skills = self.rule_parser.extract_cv_data(file_path, ['skills']).get('skills', '')
                    result['skills'] = self._merge_skills(adv_skills, rule_skills)
                
                if 'education' in fields:
                    # Prefer advanced parser's education
                    result['education'] = advanced_result.get('education', '')
                    if not result['education']:
                        rule_edu = self.rule_parser.extract_cv_data(file_path, ['education'])
                        result['education'] = rule_edu.get('education', '')
            
            # Seniority classification using advanced parser
            if 'seniority' in fields:
                result['seniority'] = advanced_result.get('seniority', 'Junior')
            
            return result
            
        except Exception as e:
            return {"error": f"Could not process CV: {e}"}
    
    def _extract_text_from_file(self, file_path: str) -> str:
        """Extract text from PDF or DOCX file"""
        try:
            import fitz
            with fitz.open(file_path) as doc:
                return "\f".join(page.get_text("text") for page in doc)
        except Exception as e:
            print(f"Error reading file: {e}")
            return ""
    
    def _merge_skills(self, advanced_skills: str, rule_skills: str) -> str:
        """Merge skills from both parsers, removing duplicates and filtering dates"""
        all_skills = set()

        # Add skills from advanced parser
        if advanced_skills and advanced_skills not in ['Fähigkeiten nicht spezifiziert', '']:
            for skill in advanced_skills.split(','):
                skill = skill.strip()
                if skill and self._is_valid_skill(skill):
                    all_skills.add(skill)

        # Add skills from rule parser (but filter out obvious non-skills)
        if rule_skills and rule_skills not in ['Fähigkeiten nicht spezifiziert', 'R', '']:
            for skill in rule_skills.split(','):
                skill = skill.strip()
                if skill and self._is_valid_skill(skill):
                    all_skills.add(skill)

        if all_skills:
            return ", ".join(sorted(all_skills)[:15])  # Limit to 15 skills

        return "Fähigkeiten nicht spezifiziert"

    def _is_valid_skill(self, skill: str) -> bool:
        """Check if a string is a valid skill (not a date, address, etc.)"""
        if len(skill) < 2:
            return skill.lower() in ['r', 'c']  # Allow single letter programming languages

        # Filter out dates and date ranges
        if re.match(r'^\d{2}[./]\d{2}[./]\d{2,4}$', skill):
            return False
        if re.match(r'^\d{4}[-–]\d{4}$', skill):
            return False
        if re.match(r'^\d{2}/\d{4}$', skill):
            return False
        if re.match(r'^\d{2}\.\d{4}$', skill):
            return False
        if re.match(r'^\d{2}/\d{4}\s*[-–]\s*\d{2}/\d{4}$', skill):
            return False
        if re.match(r'^\d{2}\.\d{4}\s*[-–]\s*\d{2}\.\d{4}$', skill):
            return False

        # Filter out obvious addresses/locations
        if re.match(r'^\d{5}\s+[A-Z]', skill):  # PLZ + city
            return False

        # Filter out pure numbers
        if skill.isdigit():
            return False

        # Filter out email-like strings
        if '@' in skill:
            return False

        # Filter out phone-like strings
        if re.match(r'^[\d\s\-/+()]+$', skill):
            return False

        # Filter out obvious non-skills
        exclude_terms = [
            'adresse', 'telefon', 'email', 'geburtsdatum', 'staatsangehörigkeit',
            'familienstand', 'straße', 'plz', 'ort', 'deutschland', 'germany',
            'aufgaben', 'westring', 'zügig', 'zuverlässig', 'erledigen'
        ]
        if skill.lower() in exclude_terms:
            return False

        # Filter out strings that are mostly punctuation or numbers
        if len(re.sub(r'[^a-zA-ZäöüÄÖÜß]', '', skill)) < 3:
            return False

        return True
    
    def extract_cv_data_bilingual(self, file_path: str, fields: List[str] = None) -> Dict[str, str]:
        """Bilingual CV data extraction (compatibility method for app.py)"""
        return self.extract_cv_data(file_path, fields)

    def get_parser_info(self) -> Dict[str, str]:
        """Get information about the hybrid parser"""
        return {
            "name": "Hybrid German CV Parser",
            "version": "1.0",
            "features": [
                "Rule-based name/email/phone extraction",
                "spaCy-powered skills detection",
                "Advanced experience calculation",
                "German language optimization",
                "Hybrid skill merging"
            ],
            "spacy_available": str(self.advanced_parser.nlp_available)
        }

# Backward compatibility
GermanCVParserHybrid = HybridGermanCVParser
