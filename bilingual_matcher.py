"""
Bilingual CV Matcher
Enhanced CV matching with German and English language support
"""

import re
import math
from typing import List, Tuple, Dict, Set
from collections import Counter
from language_detector import LanguageDetector
from german_skills import GermanSkills, GERMAN_STOP_WORDS
from job_specific_matcher import JobSpecificMatcher


class BilingualCVMatcher(JobSpecificMatcher):
    def __init__(self):
        super().__init__()
        self.language_detector = LanguageDetector()
        self.german_skills = GermanSkills()

        # Define stop words for bilingual matching
        english_stop_words = {
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before',
            'after', 'above', 'below', 'between', 'among', 'throughout', 'despite',
            'towards', 'upon', 'concerning', 'regarding', 'within', 'without'
        }
        self.bilingual_stop_words = english_stop_words.union(GERMAN_STOP_WORDS)
        
        # Enhanced skill sets for bilingual matching
        self.enhanced_technical_skills = [
            # Programming languages
            'python', 'java', 'javascript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust',
            'typescript', 'kotlin', 'swift', 'scala', 'r', 'matlab', 'sql',
            
            # German technical terms
            'programmierung', 'softwareentwicklung', 'webentwicklung', 'datenbank',
            'algorithmus', 'datenstrukturen', 'objektorientiert', 'agile entwicklung',
            
            # Web technologies
            'react', 'angular', 'vue', 'node', 'express', 'django', 'flask',
            'spring', 'bootstrap', 'jquery', 'html', 'css', 'frontend', 'backend',
            
            # Databases
            'mysql', 'postgresql', 'mongodb', 'redis', 'sqlite', 'oracle',
            'datenbankdesign', 'datenbankadministration',
            
            # Tools and platforms
            'git', 'docker', 'kubernetes', 'aws', 'azure', 'linux', 'windows',
            'jenkins', 'gitlab', 'github', 'jira', 'confluence'
        ]
        
        self.enhanced_soft_skills = [
            # English soft skills
            'leadership', 'communication', 'teamwork', 'management', 'analysis',
            'problem solving', 'critical thinking', 'project management',
            
            # German soft skills
            'führung', 'führungskompetenz', 'kommunikation', 'teamarbeit',
            'projektmanagement', 'problemlösung', 'analytisches denken',
            'kritisches denken', 'zusammenarbeit', 'kooperation'
        ]
    
    def preprocess_text_bilingual(self, text: str) -> List[str]:
        """Enhanced text preprocessing for bilingual content"""
        # Convert to lowercase
        text = text.lower()
        
        # Handle German umlauts and special characters
        text = text.replace('ä', 'ae').replace('ö', 'oe').replace('ü', 'ue').replace('ß', 'ss')
        
        # Remove special characters but keep German word boundaries
        text = re.sub(r'[^a-zA-Z0-9\s\-]', ' ', text)
        
        # Split into words
        words = text.split()
        
        # Remove stop words and short words
        words = [word for word in words if word not in self.bilingual_stop_words and len(word) > 2]
        
        return words
    
    def calculate_bilingual_similarity(self, job_description: str, cv_content: str) -> Dict[str, float]:
        """Calculate similarity scores with bilingual support"""
        # Detect languages
        job_lang, job_conf = self.language_detector.detect_language(job_description)
        cv_lang, cv_conf = self.language_detector.detect_language(cv_content)
        
        # Calculate different similarity metrics
        tf_idf_score = self.calculate_tf_idf_similarity_bilingual(job_description, cv_content)
        keyword_score = self.calculate_keyword_match_bilingual(job_description, cv_content)
        skill_score = self.calculate_skill_match_bilingual(job_description, cv_content)
        language_bonus = self.calculate_language_compatibility_bonus(job_lang, cv_lang, job_conf, cv_conf)
        
        return {
            'tf_idf': tf_idf_score,
            'keyword': keyword_score,
            'skill': skill_score,
            'language_bonus': language_bonus,
            'job_language': job_lang,
            'cv_language': cv_lang
        }
    
    def calculate_tf_idf_similarity_bilingual(self, job_description: str, cv_content: str) -> float:
        """Enhanced TF-IDF calculation for bilingual content"""
        # Use bilingual preprocessing
        job_words = self.preprocess_text_bilingual(job_description)
        cv_words = self.preprocess_text_bilingual(cv_content)
        
        # Create vocabulary
        all_words = set(job_words + cv_words)
        
        if not all_words:
            return 0.0
        
        # Calculate term frequencies
        job_tf = Counter(job_words)
        cv_tf = Counter(cv_words)
        
        # Calculate similarity using cosine similarity
        dot_product = 0
        job_magnitude = 0
        cv_magnitude = 0
        
        for word in all_words:
            job_freq = job_tf.get(word, 0)
            cv_freq = cv_tf.get(word, 0)
            
            dot_product += job_freq * cv_freq
            job_magnitude += job_freq ** 2
            cv_magnitude += cv_freq ** 2
        
        if job_magnitude == 0 or cv_magnitude == 0:
            return 0.0
        
        similarity = dot_product / (math.sqrt(job_magnitude) * math.sqrt(cv_magnitude))
        return similarity
    
    def calculate_keyword_match_bilingual(self, job_description: str, cv_content: str) -> float:
        """Enhanced keyword matching for bilingual content"""
        job_words = set(self.preprocess_text_bilingual(job_description))
        cv_words = set(self.preprocess_text_bilingual(cv_content))
        
        if not job_words:
            return 0.0
        
        # Calculate Jaccard similarity
        intersection = job_words.intersection(cv_words)
        union = job_words.union(cv_words)
        
        if not union:
            return 0.0
        
        return len(intersection) / len(union)
    
    def calculate_skill_match_bilingual(self, job_description: str, cv_content: str) -> float:
        """Enhanced skill matching for bilingual content"""
        job_lower = job_description.lower()
        cv_lower = cv_content.lower()
        
        # Find skills in job description
        job_technical_skills = [skill for skill in self.enhanced_technical_skills if skill in job_lower]
        job_soft_skills = [skill for skill in self.enhanced_soft_skills if skill in job_lower]
        job_skills = job_technical_skills + job_soft_skills
        
        # Find skills in CV
        cv_technical_skills = [skill for skill in self.enhanced_technical_skills if skill in cv_lower]
        cv_soft_skills = [skill for skill in self.enhanced_soft_skills if skill in cv_lower]
        cv_skills = cv_technical_skills + cv_soft_skills
        
        if not job_skills:
            return 0.0
        
        # Calculate skill match with weights
        matched_technical = set(job_technical_skills).intersection(set(cv_technical_skills))
        matched_soft = set(job_soft_skills).intersection(set(cv_soft_skills))
        
        # Weight technical skills higher
        technical_score = len(matched_technical) / max(len(job_technical_skills), 1) * 0.7
        soft_score = len(matched_soft) / max(len(job_soft_skills), 1) * 0.3
        
        return technical_score + soft_score
    
    def calculate_language_compatibility_bonus(self, job_lang: str, cv_lang: str, 
                                             job_conf: float, cv_conf: float) -> float:
        """Calculate bonus score for language compatibility"""
        # Perfect match bonus
        if job_lang == cv_lang and job_conf > 0.7 and cv_conf > 0.7:
            return 0.1
        
        # Bilingual content bonus
        if (job_lang == 'mixed' or cv_lang == 'mixed') and job_conf > 0.5 and cv_conf > 0.5:
            return 0.05
        
        # Different languages but high confidence - small penalty
        if job_lang != cv_lang and job_conf > 0.8 and cv_conf > 0.8:
            return -0.02
        
        return 0.0
    
    def match_bilingual(self, job_description: str, cv_contents: List[str]) -> List[Tuple[float, str, Dict]]:
        """Enhanced matching with bilingual support and detailed scoring"""
        results = []
        
        for i, cv_content in enumerate(cv_contents):
            # Calculate bilingual similarity scores
            scores = self.calculate_bilingual_similarity(job_description, cv_content)
            
            # Weighted combination of scores with language bonus
            overall_score = (
                scores['tf_idf'] * 0.35 +
                scores['keyword'] * 0.25 +
                scores['skill'] * 0.35 +
                scores['language_bonus'] * 0.05
            )
            
            # Ensure score is between 0 and 1
            overall_score = max(0.0, min(1.0, overall_score))
            
            results.append((overall_score, cv_content, scores))
        
        # Sort by score in descending order
        results.sort(key=lambda x: x[0], reverse=True)
        
        return results
    
    def get_detailed_match_explanation(self, job_description: str, cv_content: str) -> Dict[str, any]:
        """Get detailed explanation of match score with bilingual insights"""
        scores = self.calculate_bilingual_similarity(job_description, cv_content)
        
        overall_score = (
            scores['tf_idf'] * 0.35 +
            scores['keyword'] * 0.25 +
            scores['skill'] * 0.35 +
            scores['language_bonus'] * 0.05
        )
        
        # Find common keywords
        job_words = set(self.preprocess_text_bilingual(job_description))
        cv_words = set(self.preprocess_text_bilingual(cv_content))
        common_keywords = job_words.intersection(cv_words)
        
        # Find matched skills
        job_lower = job_description.lower()
        cv_lower = cv_content.lower()
        
        matched_technical = []
        matched_soft = []
        
        for skill in self.enhanced_technical_skills:
            if skill in job_lower and skill in cv_lower:
                matched_technical.append(skill)
        
        for skill in self.enhanced_soft_skills:
            if skill in job_lower and skill in cv_lower:
                matched_soft.append(skill)
        
        return {
            'overall_score': overall_score * 100,
            'component_scores': {
                'content_similarity': scores['tf_idf'] * 100,
                'keyword_match': scores['keyword'] * 100,
                'skill_match': scores['skill'] * 100,
                'language_bonus': scores['language_bonus'] * 100
            },
            'language_info': {
                'job_language': scores['job_language'],
                'cv_language': scores['cv_language']
            },
            'matched_content': {
                'common_keywords': list(common_keywords)[:15],
                'technical_skills': matched_technical[:10],
                'soft_skills': matched_soft[:10]
            },
            'explanation': self._generate_explanation(scores, overall_score),
            'recommendations': self._generate_recommendations(scores, matched_technical, matched_soft)
        }
    
    def _generate_explanation(self, scores: Dict[str, float], overall_score: float) -> str:
        """Generate human-readable explanation of the match"""
        explanation = f"Gesamtübereinstimmung: {overall_score*100:.1f}% "
        explanation += f"(Inhaltsähnlichkeit: {scores['tf_idf']*100:.1f}%, "
        explanation += f"Schlüsselwort-Match: {scores['keyword']*100:.1f}%, "
        explanation += f"Fähigkeiten-Match: {scores['skill']*100:.1f}%"
        
        if scores['language_bonus'] != 0:
            explanation += f", Sprachbonus: {scores['language_bonus']*100:.1f}%"
        
        explanation += ")"
        
        return explanation
    
    def _generate_recommendations(self, scores: Dict, technical_skills: List, soft_skills: List) -> List[str]:
        """Generate recommendations based on match analysis"""
        recommendations = []
        
        if scores['overall_score'] > 0.8:
            recommendations.append("Ausgezeichnete Übereinstimmung - sehr empfehlenswert für ein Interview")
        elif scores['overall_score'] > 0.6:
            recommendations.append("Gute Übereinstimmung - für weitere Prüfung geeignet")
        elif scores['overall_score'] > 0.4:
            recommendations.append("Moderate Übereinstimmung - manuelle Überprüfung empfohlen")
        else:
            recommendations.append("Geringe Übereinstimmung - möglicherweise nicht geeignet")
        
        if len(technical_skills) > 5:
            recommendations.append("Starke technische Fähigkeiten vorhanden")
        
        if len(soft_skills) > 3:
            recommendations.append("Gute Soft Skills identifiziert")
        
        if scores['language_bonus'] > 0:
            recommendations.append("Sprachkompatibilität ist ein Vorteil")
        
        return recommendations


# Utility functions for easy integration
def match_cvs_bilingual(job_description: str, cv_contents: List[str]) -> List[Tuple[float, str, Dict]]:
    """Quick function for bilingual CV matching"""
    matcher = BilingualCVMatcher()
    return matcher.match_bilingual(job_description, cv_contents)


def get_bilingual_match_explanation(job_description: str, cv_content: str) -> Dict[str, any]:
    """Get detailed bilingual match explanation"""
    matcher = BilingualCVMatcher()
    return matcher.get_detailed_match_explanation(job_description, cv_content)
